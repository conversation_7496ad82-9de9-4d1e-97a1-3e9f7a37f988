/* ========================================
   PORTAL MODE STYLES - REFACTORED
   ======================================== */

/* ========================================
   1. IMPORTS & RESET STYLES
   ======================================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* ========================================
   2. LAYOUT CONTAINERS
   ======================================== */

/* Root portal container */
#portal-root {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Main portal container */
.portal-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: fadeIn 0.6s ease-out;
}

/* ========================================
   3. CORE ANIMATIONS
   ======================================== */

/* Base fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide up animation for chat area */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Message entrance animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse animation for status indicators */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Subtle pulse for send button */
@keyframes subtlePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* Fade in with delay for read indicators */
@keyframes fadeInDelay {
  to { opacity: 1; }
}

/* ========================================
   4. CHAT AREA LAYOUT
   ======================================== */

/* Main chat area container - Three-panel layout */
.portal-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: white;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.8s ease-out 0.2s both;
  height: 100vh;
  position: relative;
}

/* ========================================
   5. ERROR & WARNING COMPONENTS
   ======================================== */

/* Banner notifications */
.portal-error-banner,
.portal-warning-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  margin: 0.5rem 1rem;
  border-radius: 12px;
  animation: slideInFromTop 0.5s ease-out;
}

.portal-error-banner {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #f87171;
  color: #dc2626;
}

.portal-warning-banner {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  color: #d97706;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Error content layout */
.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-icon,
.warning-icon {
  font-size: 1.25rem;
}

.error-message,
.warning-message {
  flex: 1;
  font-weight: 500;
}

/* Error page layout */
.portal-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.portal-error .error-content {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.portal-error .error-content .error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.portal-error .error-content h1 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.portal-error .error-content p {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* ========================================
   6. BUTTON COMPONENTS
   ======================================== */

/* Retry buttons */
.retry-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* ========================================
   7. PORTAL MODE OVERRIDES
   ======================================== */

/* Hide floating chat button in portal mode */
.portal-container .chat-button {
  display: none !important;
}

/* Full-screen chat container */
.portal-container .chat-container {
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
  border-radius: 0;
  box-shadow: none;
  bottom: auto;
  right: auto;
  transform: none;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  background: #f8fafc;
}

.portal-container .chat-container.portal-mode {
  height: 100%;
}

/* ========================================
   8. CHAT HEADER COMPONENT
   ======================================== */

.chat-header.portal-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0;
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  width: 100%;
  box-sizing: border-box;
}

/* ========================================
   9. CHAT MESSAGES COMPONENT
   ======================================== */

.chat-messages.portal-mode {
  flex: 1;
  min-height: 0;
  height: calc(100vh - 140px);
  padding: 1.5rem;
  background: #f8fafc;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  overflow-y: auto;
  padding-top: 60px;
  margin-bottom: 140px;
  box-sizing: border-box;
}

/* Custom scrollbar styling */
.chat-messages.portal-mode::-webkit-scrollbar {
  width: 8px;
}

.chat-messages.portal-mode::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin: 8px 0;
}

.chat-messages.portal-mode::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.chat-messages.portal-mode::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Firefox scrollbar styling */
.chat-messages.portal-mode {
  scrollbar-width: thin;
  scrollbar-color: #667eea rgba(0, 0, 0, 0.05);
}

/* ========================================
   10. CHAT INPUT COMPONENT
   ======================================== */

/* Main input container - sticky to bottom */
.chat-input.portal-mode {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid #e2e8f0;
  border-radius: 0;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

/* Input row layout */
.chat-input.portal-mode .input-row {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* Textarea styling */
.chat-input.portal-mode .portal-textarea {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px;
  max-height: 120px;
  line-height: 1.4;
  font-family: inherit;
  resize: none;
  overflow: hidden;
  box-sizing: border-box;
  width: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-input.portal-mode .portal-textarea:not(:placeholder-shown) {
  white-space: normal;
}

.chat-input.portal-mode .portal-textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: white;
  outline: none;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.chat-input.portal-mode .portal-textarea:not(:placeholder-shown):not(:focus) {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.1);
}

.chat-input.portal-mode .portal-textarea::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Legacy input support (fallback) */
.chat-input.portal-mode .portal-input {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px;
  box-sizing: border-box;
  width: 0;
}

.chat-input.portal-mode .portal-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

/* Button container */
.chat-input.portal-mode .portal-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  flex-wrap: nowrap;
  min-width: fit-content;
}

/* Icon buttons */
.chat-input.portal-mode .icon-button {
  padding: 0.75rem;
  font-size: 1.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
}

.chat-input.portal-mode .icon-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #e2e8f0;
}

.chat-input.portal-mode .icon-button:active {
  transform: scale(0.95);
  background-color: #cbd5e1;
}

/* Send button styling */
.chat-input.portal-mode .send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.chat-input.portal-mode .send-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.chat-input.portal-mode .send-button:disabled {
  background: #cbd5e1;
  color: #64748b;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.chat-input.portal-mode .send-button:not(:disabled) {
  animation: subtlePulse 2s infinite;
}

/* Specific button hover states */
.chat-input.portal-mode .attach-button:hover {
  background: #dbeafe;
  border-color: #3b82f6;
}

.chat-input.portal-mode .camera-button:hover {
  background: #dcfce7;
  border-color: #22c55e;
}

/* ========================================
   11. ENHANCED UX COMPONENTS
   ======================================== */

/* Message enhancements */
.message-container {
  animation: slideInUp 0.3s ease-out;
}

.message-container:hover .message {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.message-container.user .message::after {
  content: '✓';
  position: absolute;
  bottom: 4px;
  right: 8px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  opacity: 0;
  animation: fadeInDelay 1s ease 0.5s forwards;
}

/* Loading indicator */
.loading-dots {
  background: rgba(255, 255, 255, 0.9);
  padding: 12px 16px;
  border-radius: 18px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Connection status indicator */
.connection-indicator {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(34, 197, 94, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  z-index: 1001;
  backdrop-filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.connection-indicator.show {
  opacity: 1;
}

.connection-indicator.offline {
  background: rgba(239, 68, 68, 0.9);
}

/* Scroll to bottom button */
.scroll-to-bottom {
  position: fixed;
  bottom: 140px;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  z-index: 999;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.scroll-to-bottom.show {
  opacity: 1;
  transform: translateY(0);
}

.scroll-to-bottom:hover {
  background: rgba(102, 126, 234, 1);
  transform: scale(1.1);
}

/* ========================================
   12. ACCESSIBILITY & FOCUS MANAGEMENT
   ======================================== */

/* Focus styles */
.chat-input.portal-mode .portal-input:focus,
.chat-input.portal-mode .portal-textarea:focus {
  outline: none;
}

.chat-input.portal-mode .icon-button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth transitions */
.chat-input.portal-mode .portal-textarea {
  transition: height 0.2s ease-out, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chat-input.portal-mode .icon-button,
  .chat-input.portal-mode .portal-input,
  .chat-input.portal-mode .portal-textarea {
    transition: none;
  }
}

/* ========================================
   13. RESPONSIVE DESIGN
   ======================================== */

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .chat-input.portal-mode .icon-button:hover {
    transform: none;
    box-shadow: none;
  }

  .chat-input.portal-mode .icon-button:active {
    transform: scale(0.95);
    background: #e2e8f0;
  }

  .chat-input.portal-mode .send-button:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }

  .chat-input.portal-mode .attach-button:active {
    background: #dbeafe;
    border-color: #3b82f6;
  }

  .chat-input.portal-mode .camera-button:active {
    background: #dcfce7;
    border-color: #22c55e;
  }
}

/* Small mobile styles (max-width: 480px) */
@media (max-width: 480px) {
  .chat-input.portal-mode {
    padding: 0.75rem;
    padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.2);
  }

  .chat-input.portal-mode .input-row {
    gap: 0.375rem;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px;
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
    max-height: 80px;
  }

  .chat-input.portal-mode .portal-textarea::placeholder {
    font-size: 13px;
  }

  .chat-input.portal-mode .portal-input {
    font-size: 16px;
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.5rem;
    font-size: 1rem;
    min-width: 36px;
    min-height: 36px;
    border-radius: 8px;
    flex-shrink: 0;
  }

  .chat-input.portal-mode .portal-buttons {
    gap: 0.25rem;
    flex-shrink: 0;
  }

  .chat-messages.portal-mode {
    padding: 0.75rem;
    height: calc(100vh - 100px);
    padding-top: calc(50px + 0.75rem);
    margin-bottom: 100px;
  }

  .chat-header.portal-mode {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
  }
}

/* Desktop styles (min-width: 769px) */
@media (min-width: 769px) {
  .portal-chat-area {
    border-radius: 0;
    box-shadow: none;
    max-width: none;
    height: 100%;
  }

  .chat-header.portal-mode {
    padding: 1.25rem 2rem;
    font-size: 1.2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
  }

  .chat-messages.portal-mode {
    padding: 2rem;
    height: calc(100vh - 160px);
    padding-top: 70px;
    margin-bottom: 80px;
  }

  .chat-input.portal-mode {
    padding: 1.5rem 2rem;
  }

  .chat-messages.portal-mode::-webkit-scrollbar {
    width: 12px;
  }

  .chat-messages.portal-mode::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 6px;
  }

  .chat-messages.portal-mode::-webkit-scrollbar-thumb {
    border-radius: 6px;
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .chat-input.portal-mode {
    padding: 0.75rem 1rem;
  }

  .chat-messages.portal-mode {
    padding: 0.75rem 1rem;
    height: calc(100vh - 90px);
    padding-top: calc(45px + 0.75rem);
    margin-bottom: 90px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chat-input.portal-mode .icon-button {
    border-width: 0.5px;
  }
}
