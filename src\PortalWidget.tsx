import React from 'react';
import ChatWidget from './ChatWidget';
import MockChatWidget from './MockChatWidget';

interface PortalWidgetProps {
  apiKey: string;
  apiUrl: string;
  tradieName: string;
}

const PortalWidget: React.FC<PortalWidgetProps> = ({ apiKey, apiUrl, tradieName }) => {
  console.info('🎭 PortalWidget: Rendering with props:', { apiKey: apiKey?.substring(0, 10) + '...', apiUrl, tradieName });

  // Determine if we should use mock widget based on tradie name
  const isDemoMode = tradieName === 'demo';

  return (
    <div className="portal-container">
      <div className="portal-chat-area">
        {/* Use MockChatWidget only for demo mode, otherwise use real ChatWidget */}
        {isDemoMode ? (
          <MockChatWidget
            apiKey={apiKey}
            apiUrl={apiUrl}
            isPortalMode={true}
            tradieName={tradieName}
          />
        ) : (
          <ChatWidget
            apiKey={apiKey}
            apiUrl={apiUrl}
            isPortalMode={true}
            tradieName={tradieName}
          />
        )}
      </div>
    </div>
  );
};

export default PortalWidget;
